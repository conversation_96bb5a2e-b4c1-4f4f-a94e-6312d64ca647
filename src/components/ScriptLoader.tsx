'use client';

import Script from 'next/script';

export default function ScriptLoader() {
  return (
    <>
      {/* jQuery Library File */}
      <Script 
        src="/js/jquery-3.7.1.min.js" 
        strategy="afterInteractive"
        onLoad={() => {
          if (typeof window !== 'undefined') {
            window.jQueryLoaded = true;
          }
        }}
      />
      
      {/* Bootstrap JS file */}
      <Script 
        src="/js/bootstrap.min.js" 
        strategy="afterInteractive"
        onLoad={() => {
          if (typeof window !== 'undefined') {
            window.bootstrapLoaded = true;
          }
        }}
      />
      
      {/* Validator JS file */}
      <Script src="/js/validator.min.js" strategy="afterInteractive" />
      
      {/* SlickNav JS file */}
      <Script src="/js/jquery.slicknav.js" strategy="afterInteractive" />
      
      {/* Swiper JS file */}
      <Script src="/js/swiper-bundle.min.js" strategy="afterInteractive" />
      
      {/* Counter JS file - Load after jQuery */}
      <Script 
        src="/js/jquery.waypoints.min.js" 
        strategy="afterInteractive"
        onLoad={() => {
          if (typeof window !== 'undefined' && window.jQuery) {
            window.waypointsLoaded = true;
            // Ensure Waypoints is properly attached to jQuery
            if (window.jQuery.fn && window.jQuery.fn.waypoint) {
              console.log('Waypoints plugin loaded successfully');
            }
          }
        }}
      />
      <Script 
        src="/js/jquery.counterup.min.js" 
        strategy="afterInteractive"
        onLoad={() => {
          if (typeof window !== 'undefined' && window.jQuery) {
            window.counterupLoaded = true;
            // Ensure CounterUp is properly attached to jQuery
            if (window.jQuery.fn && window.jQuery.fn.counterUp) {
              console.log('CounterUp plugin loaded successfully');
            }
          }
        }}
      />
      
      {/* Magnific JS file */}
      <Script src="/js/jquery.magnific-popup.min.js" strategy="afterInteractive" />
      
      {/* SmoothScroll */}
      <Script src="/js/SmoothScroll.js" strategy="afterInteractive" />
      
      {/* Parallax JS */}
      <Script src="/js/parallaxie.js" strategy="afterInteractive" />
      
      {/* MagicCursor JS file */}
      <Script src="/js/gsap.min.js" strategy="afterInteractive" />
      <Script src="/js/magiccursor.js" strategy="afterInteractive" />
      
      {/* Text Effect JS file */}
      <Script src="/js/SplitText.js" strategy="afterInteractive" />
      <Script src="/js/ScrollTrigger.min.js" strategy="afterInteractive" />
      
      {/* YTPlayer JS File */}
      <Script src="/js/jquery.mb.YTPlayer.min.js" strategy="afterInteractive" />
      
      {/* Wow JS file - Load before function.js */}
      <Script 
        src="/js/wow.min.js" 
        strategy="afterInteractive"
        onLoad={() => {
          if (typeof window !== 'undefined') {
            window.wowLoaded = true;
            console.log('WOW.js loaded successfully');
          }
        }}
      />
      
      {/* Main Custom JS file - Load after all dependencies */}
      <Script 
        src="/js/function.js" 
        strategy="afterInteractive"
        onLoad={() => {
          if (typeof window !== 'undefined' && window.wowLoaded) {
            window.functionJsLoaded = true;
            console.log('Function.js loaded successfully after WOW.js');
          } else {
            // Retry after a short delay if WOW.js isn't loaded yet
            setTimeout(() => {
              if (typeof window !== 'undefined') {
                window.functionJsLoaded = true;
                console.log('Function.js loaded successfully (delayed)');
              }
            }, 1000);
          }
        }}
      />
    </>
  );
}
