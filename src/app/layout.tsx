import type { Metadata } from 'next'
import Script from 'next/script'
import './globals.css'
import ScriptLoader from '@/components/ScriptLoader'
import Navbar from '@/components/layout/Navbar'

export const metadata: Metadata = {
  title: 'Firevall - Cyber Security Services',
  description: 'Advanced cybersecurity solutions tailored to keep you safe',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <head>
        {/* Google Fonts */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />
        <link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet" />
        
        {/* Bootstrap CSS */}
        <link href="/css/bootstrap.min.css" rel="stylesheet" media="screen" />
        
        {/* SlickNav CSS */}
        <link href="/css/slicknav.min.css" rel="stylesheet" />
        
        {/* Swiper CSS */}
        <link rel="stylesheet" href="/css/swiper-bundle.min.css" />
        
        {/* Font Awesome Icon CSS */}
        <link href="/css/all.min.css" rel="stylesheet" media="screen" />
        
        {/* Animated CSS */}
        <link href="/css/animate.css" rel="stylesheet" />
        
        {/* Magnific Popup Core CSS File */}
        <link rel="stylesheet" href="/css/magnific-popup.css" />
        
        {/* Mouse Cursor CSS File */}
        <link rel="stylesheet" href="/css/mousecursor.css" />
        
        {/* Main Custom CSS */}
        <link href="/css/custom.css" rel="stylesheet" media="screen" />
      </head>
      <body>
        <Navbar />
        {children}
        <ScriptLoader />
      </body>
    </html>
  )
}
