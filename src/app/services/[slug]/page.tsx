'use client';

import { useEffect } from 'react';
import { use } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import Footer from '@/components/layout/Footer';
import PageHeader from '@/components/layout/PageHeader';
import ScrollingTicker from '@/components/layout/ScrollingTicker';
import { useScriptInitialization } from '@/hooks/useScriptInitialization';

// Service data - you can expand this with more services
const servicesData = {
  'cybersecurity-consulting': {
    title: 'Cybersecurity Consulting Services',
    description: 'Our comprehensive cybersecurity consulting services help organizations identify vulnerabilities, implement security measures, and maintain robust protection against evolving threats.',
    overview: 'We provide expert cybersecurity consulting to help your organization build a strong security foundation. Our team of certified professionals works closely with you to assess your current security posture, identify potential risks, and develop comprehensive security strategies.',
    image: '/images/service-single-img.jpg',
    features: [
      'Security Assessment & Auditing',
      'Risk Management & Compliance',
      'Incident Response Planning',
      'Security Policy Development',
      'Employee Security Training',
      'Ongoing Security Monitoring'
    ]
  },
  'data-encryption': {
    title: 'Data Encryption Services',
    description: 'Advanced data encryption solutions to protect your sensitive information with industry-standard encryption protocols and secure key management.',
    overview: 'Our data encryption services ensure that your sensitive information remains protected both at rest and in transit. We implement robust encryption protocols and secure key management systems.',
    image: '/images/service-single-img.jpg',
    features: [
      'End-to-End Encryption',
      'Key Management Systems',
      'Data-at-Rest Protection',
      'Data-in-Transit Security',
      'Compliance Standards',
      'Regular Security Audits'
    ]
  },
  'threat-monitoring': {
    title: 'Threat Monitoring & Detection',
    description: '24/7 threat monitoring and detection services using advanced AI and machine learning to identify and respond to security threats in real-time.',
    overview: 'Our threat monitoring services provide round-the-clock surveillance of your digital assets using cutting-edge AI and machine learning technologies.',
    image: '/images/service-single-img.jpg',
    features: [
      'Real-Time Threat Detection',
      'AI-Powered Analysis',
      'Incident Response',
      'Threat Intelligence',
      'Security Alerts',
      'Performance Reporting'
    ]
  },
  'incident-response': {
    title: 'Incident Response Services',
    description: 'Rapid incident response and recovery services to minimize damage and restore operations quickly when security incidents occur.',
    overview: 'When security incidents occur, our rapid response team is ready to minimize damage and restore your operations quickly.',
    image: '/images/service-single-img.jpg',
    features: [
      '24/7 Emergency Response',
      'Forensic Analysis',
      'System Recovery',
      'Communication Management',
      'Legal Compliance',
      'Post-Incident Review'
    ]
  },
  'security-training': {
    title: 'Security Awareness Training',
    description: 'Comprehensive security training programs to educate your employees about cybersecurity best practices and threat prevention.',
    overview: 'Human error is often the weakest link in cybersecurity. Our training programs educate your employees about security best practices.',
    image: '/images/service-single-img.jpg',
    features: [
      'Phishing Awareness',
      'Password Security',
      'Social Engineering Defense',
      'Data Protection Training',
      'Compliance Education',
      'Regular Assessments'
    ]
  }
};

interface ServicePageProps {
  params: Promise<{
    slug: string;
  }>;
}

export default function ServicePage({ params }: ServicePageProps) {
  const isScriptsInitialized = useScriptInitialization();
  const { slug } = use(params);
  
  // Get service data based on slug
  const service = servicesData[slug as keyof typeof servicesData];
  
  // If service not found, you could redirect to 404 or show a default
  if (!service) {
    return (
      <div className="container mt-5">
        <div className="row">
          <div className="col-12">
            <h1>Service Not Found</h1>
            <p>The requested service could not be found.</p>
            <Link href="/services" className="btn btn-primary">
              Back to Services
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Preloader Start */}
      <div className="preloader">
        <div className="loading-container">
          <div className="loading" />
          <div id="loading-icon">
            <Image src="/images/loader.svg" alt="Loader" width={50} height={50} />
          </div>
        </div>
      </div>
      {/* Preloader End */}

      <PageHeader 
        title={service.title}
        breadcrumbs={[
          { label: 'home', href: '/' },
          { label: 'services', href: '/services' },
          { label: service.title, isActive: true }
        ]}
      />

      <ScrollingTicker />

      {/* Page Service Single Start */}
      <div className="page-service-single">
        <div className="container">
          <div className="row">
            <div className="col-lg-4">
              {/* Page Single Sidebar Start */}
              <div className="page-single-sidebar">
                {/* Service Category List Start */}
                <div className="page-catagory-list wow fadeInUp">
                  <h3>Service category</h3>
                  <ul>
                    {Object.keys(servicesData).map((serviceSlug) => {
                      const serviceData = servicesData[serviceSlug as keyof typeof servicesData];
                      return (
                        <li key={serviceSlug}>
                          <Link href={`/services/${serviceSlug}`}>
                            {serviceData.title}
                          </Link>
                        </li>
                      );
                    })}
                  </ul>
                </div>
                {/* Service Category List End */}

                {/* Sidebar CTA Box Start */}
                <div className="sidebar-cta-box wow fadeInUp" data-wow-delay="0.25s">
                  {/* Sidebar CTA Image Start */}
                  <div className="sidebar-cta-img">
                    <Image src="/images/sidebar-cta-img.svg" alt="CTA" width={200} height={150} />
                  </div>
                  {/* Sidebar CTA Image End */}

                  {/* Sidebar CTA Content Start */}
                  <div className="sidebar-cta-content">
                    <h3>How Can We Help</h3>
                    <p>Whenever you need help, our team is just a message or call away.</p>
                  </div>
                  {/* Sidebar CTA Content End */}

                  {/* Sidebar CTA Contact Start */}
                  <div className="sidebar-cta-contact">
                    {/* Footer Contact Item Start */}
                    <div className="footer-contact-item">
                      <div className="icon-box">
                        <i className="fa-solid fa-phone" />
                      </div>
                      <div className="footer-contact-content">
                        <p>
                          <a href="tel:+001234567">+****************</a>
                        </p>
                      </div>
                    </div>
                    {/* Footer Contact Item End */}

                    {/* Footer Contact Item Start */}
                    <div className="footer-contact-item">
                      <div className="icon-box">
                        <i className="fa-solid fa-envelope" />
                      </div>
                      <div className="footer-contact-content">
                        <p>
                          <a href="mailto:<EMAIL>"><EMAIL></a>
                        </p>
                      </div>
                    </div>
                    {/* Footer Contact Item End */}
                  </div>
                  {/* Sidebar CTA Contact End */}
                </div>
                {/* Sidebar CTA Box End */}
              </div>
              {/* Page Single Sidebar End */}
            </div>

            <div className="col-lg-8">
              {/* Service Single Content Start */}
              <div className="service-single-content">
                {/* Service Featured Image Start */}
                <div className="service-featured-image">
                  <figure className="image-anime reveal">
                    <Image src={service.image} alt={service.title} width={800} height={400} />
                  </figure>
                </div>
                {/* Service Featured Image End */}

                {/* Service Entry Start */}
                <div className="service-entry">
                  <p className="wow fadeInUp">{service.overview}</p>
                  <p className="wow fadeInUp" data-wow-delay="0.2s">
                    Our comprehensive approach ensures that every aspect of your security needs is addressed with precision and expertise.
                  </p>

                  {/* Service Solution Box Start */}
                  <div className="service-solution-box">
                    <h2 className="wow fadeInUp" data-wow-delay="0.4s">
                      Step-by-step solutions tailored to <span>your needs</span>
                    </h2>
                    <p className="wow fadeInUp" data-wow-delay="0.6s">
                      We provide step-by-step solutions designed specifically for your unique challenges. Our team analyzes your requirements, develops customized strategies, and ensures seamless implementation.
                    </p>

                    {/* Service Solution Steps Start */}
                    <div className="service-solution-steps">
                      {/* Service Solution Step Item Start */}
                      <div className="service-solution-step-item wow fadeInUp">
                        <div className="solution-step-no">
                          <h3>01</h3>
                        </div>
                        <div className="icon-box">
                          <Image src="/images/icon-specialties-item-4.svg" alt="Assessment" width={60} height={60} />
                        </div>
                        <div className="solution-step-content">
                          <h3>Assessment</h3>
                          <p>Evaluate your systems to identify vulnerabilities.</p>
                        </div>
                      </div>
                      {/* Service Solution Step Item End */}

                      {/* Service Solution Step Item Start */}
                      <div className="service-solution-step-item wow fadeInUp" data-wow-delay="0.2s">
                        <div className="solution-step-no">
                          <h3>02</h3>
                        </div>
                        <div className="icon-box">
                          <Image src="/images/icon-specialties-item-2.svg" alt="Strategy" width={60} height={60} />
                        </div>
                        <div className="solution-step-content">
                          <h3>Strategy</h3>
                          <p>Develop a customized cyber security plan based.</p>
                        </div>
                      </div>
                      {/* Service Solution Step Item End */}

                      {/* Service Solution Step Item Start */}
                      <div className="service-solution-step-item wow fadeInUp" data-wow-delay="0.4s">
                        <div className="solution-step-no">
                          <h3>03</h3>
                        </div>
                        <div className="icon-box">
                          <Image src="/images/icon-specialties-item-1.svg" alt="Implement" width={60} height={60} />
                        </div>
                        <div className="solution-step-content">
                          <h3>Implement</h3>
                          <p>Deploy advanced security measures to protect.</p>
                        </div>
                      </div>
                      {/* Service Solution Step Item End */}
                    </div>
                    {/* Service Solution Steps End */}
                  </div>
                  {/* Service Solution Box End */}

                  {/* Service Trusted Expert Start */}
                  <div className="service-trusted-expert">
                    <h2 className="wow fadeInUp">
                      Trusted experts committed to securing <span>your digital</span>
                    </h2>
                    <p className="wow fadeInUp" data-wow-delay="0.2s">
                      Our team of trusted experts is dedicated to safeguarding your digital assets with industry-leading solutions. With years of experience and a deep understanding of the latest cyber threats.
                    </p>

                    {/* Service Entry Image Start */}
                    <div className="service-entry-image">
                      <figure className="image-anime reveal">
                        <Image src="/images/case-study-image-6.jpg" alt="Service" width={800} height={400} />
                      </figure>
                    </div>
                    {/* Service Entry Image End */}

                    {/* Service Trusted Expert Item Start */}
                    <div className="service-trusted-expert-item">
                      {/* Success Item Start */}
                      <div className="success-item wow fadeInUp">
                        <div className="icon-box">
                          <Image src="/images/icon-success-item-1.svg" alt="Tailored Solutions" width={60} height={60} />
                        </div>
                        <div className="success-item-content">
                          <h3>Tailored Security Solutions</h3>
                          <p>
                            Our tailored security solutions & designed to meet the unique need of your business. By understanding your operations, vulnerabilities, and goals.
                          </p>
                        </div>
                      </div>
                      {/* Success Item End */}

                      {/* Success Item Start */}
                      <div className="success-item wow fadeInUp" data-wow-delay="0.2s">
                        <div className="icon-box">
                          <Image src="/images/icon-success-item-2.svg" alt="Advanced Technology" width={60} height={60} />
                        </div>
                        <div className="success-item-content">
                          <h3>Advanced Technology</h3>
                          <p>
                            Our advanced technology leverages cutting-edge tool & innovative methodologies to protect your business against evolving cyber threats.
                          </p>
                        </div>
                      </div>
                      {/* Success Item End */}

                      {/* Success Item Start */}
                      <div className="success-item wow fadeInUp" data-wow-delay="0.4s">
                        <div className="icon-box">
                          <Image src="/images/icon-success-item-3.svg" alt="Real-Time Detection" width={60} height={60} />
                        </div>
                        <div className="success-item-content">
                          <h3>Real-Time Threat Detection</h3>
                          <p>
                            Our Real-Time Threat Detection services ensure your system & continuously monitor to identify and respond to threats as they emerge.
                          </p>
                        </div>
                      </div>
                      {/* Success Item End */}
                    </div>
                    {/* Service Trusted Expert Item End */}
                  </div>
                  {/* Service Trusted Expert End */}
                </div>
                {/* Service Entry Image End */}

                {/* Page Single FAQs Start */}
                <div className="page-single-faqs">
                  {/* Section Title Start */}
                  <div className="section-title">
                    <h2 className="wow fadeInUp" data-cursor="-opaque">
                      Your complete FAQ guide to <span>cybersecurity solutions</span>
                    </h2>
                    <p className="wow fadeInUp" data-wow-delay="0.2s">
                      Welcome to our comprehensive FAQ guide on cybersecurity solutions. Here we answer the most common questions about protecting your business from cyber threats understanding the latest security.
                    </p>
                  </div>
                  {/* Section Title End */}

                  {/* FAQ Accordion Start */}
                  <div className="faq-accordion page-faq-accordion" id="faqaccordion">
                    {/* FAQ Item Start */}
                    <div className="accordion-item wow fadeInUp">
                      <h2 className="accordion-header" id="heading1">
                        <button
                          className="accordion-button collapsed"
                          type="button"
                          data-bs-toggle="collapse"
                          data-bs-target="#collapse1"
                          aria-expanded="true"
                          aria-controls="collapse1"
                        >
                          What types of cybersecurity services do you offer?
                        </button>
                      </h2>
                      <div
                        id="collapse1"
                        className="accordion-collapse collapse"
                        aria-labelledby="heading1"
                        data-bs-parent="#faqaccordion"
                      >
                        <div className="accordion-body">
                          <p>
                            We offer a comprehensive range of cybersecurity services including threat monitoring, incident response, data encryption, security consulting, and employee training programs.
                          </p>
                        </div>
                      </div>
                    </div>
                    {/* FAQ Item End */}

                    {/* FAQ Item Start */}
                    <div className="accordion-item wow fadeInUp" data-wow-delay="0.2s">
                      <h2 className="accordion-header" id="heading2">
                        <button
                          className="accordion-button"
                          type="button"
                          data-bs-toggle="collapse"
                          data-bs-target="#collapse2"
                          aria-expanded="false"
                          aria-controls="collapse2"
                        >
                          Do I need a security assessment before getting started?
                        </button>
                      </h2>
                      <div
                        id="collapse2"
                        className="accordion-collapse collapse show"
                        aria-labelledby="heading2"
                        data-bs-parent="#faqaccordion"
                      >
                        <div className="accordion-body">
                          <p>
                            Yes, we recommend starting with a comprehensive security assessment to identify vulnerabilities and develop a tailored security strategy for your organization.
                          </p>
                        </div>
                      </div>
                    </div>
                    {/* FAQ Item End */}

                    {/* FAQ Item Start */}
                    <div className="accordion-item wow fadeInUp" data-wow-delay="0.4s">
                      <h2 className="accordion-header" id="heading3">
                        <button
                          className="accordion-button collapsed"
                          type="button"
                          data-bs-toggle="collapse"
                          data-bs-target="#collapse3"
                          aria-expanded="false"
                          aria-controls="collapse3"
                        >
                          Are your services suitable for all business sizes?
                        </button>
                      </h2>
                      <div
                        id="collapse3"
                        className="accordion-collapse collapse"
                        aria-labelledby="heading3"
                        data-bs-parent="#faqaccordion"
                      >
                        <div className="accordion-body">
                          <p>
                            Our services are designed to scale for businesses of all sizes, from small startups to large enterprises, with customized solutions for each organization.
                          </p>
                        </div>
                      </div>
                    </div>
                    {/* FAQ Item End */}

                    {/* FAQ Item Start */}
                    <div className="accordion-item wow fadeInUp" data-wow-delay="0.6s">
                      <h2 className="accordion-header" id="heading4">
                        <button
                          className="accordion-button collapsed"
                          type="button"
                          data-bs-toggle="collapse"
                          data-bs-target="#collapse4"
                          aria-expanded="false"
                          aria-controls="collapse4"
                        >
                          Do you offer 24/7 security monitoring?
                        </button>
                      </h2>
                      <div
                        id="collapse4"
                        className="accordion-collapse collapse"
                        aria-labelledby="heading4"
                        data-bs-parent="#faqaccordion"
                      >
                        <div className="accordion-body">
                          <p>
                            Yes, we provide round-the-clock security monitoring and incident response services to ensure your systems are protected at all times.
                          </p>
                        </div>
                      </div>
                    </div>
                    {/* FAQ Item End */}

                    {/* FAQ Item Start */}
                    <div className="accordion-item wow fadeInUp" data-wow-delay="0.8s">
                      <h2 className="accordion-header" id="heading5">
                        <button
                          className="accordion-button collapsed"
                          type="button"
                          data-bs-toggle="collapse"
                          data-bs-target="#collapse5"
                          aria-expanded="false"
                          aria-controls="collapse5"
                        >
                          What should I expect during the implementation process?
                        </button>
                      </h2>
                      <div
                        id="collapse5"
                        className="accordion-collapse collapse"
                        aria-labelledby="heading5"
                        data-bs-parent="#faqaccordion"
                      >
                        <div className="accordion-body">
                          <p>
                            Our implementation process includes thorough planning, phased deployment, comprehensive testing, and ongoing support to ensure successful integration.
                          </p>
                        </div>
                      </div>
                    </div>
                    {/* FAQ Item End */}
                  </div>
                  {/* FAQ Accordion End */}
                </div>
                {/* Page Single FAQs End */}
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* Page Service Single End */}

      <Footer />
    </>
  );
}
