import { useEffect, useState } from 'react';

export const useScriptInitialization = () => {
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    let mounted = true;

    const initializeScripts = () => {
      if (!mounted) return;

      // Hide preloader with fade-out effect
      const preloader = document.querySelector('.preloader') as HTMLElement;
      if (preloader) {
        preloader.classList.add('hidden');
        setTimeout(() => {
          preloader.style.display = 'none';
        }, 500);
      }

      // Set scripts loaded flag
      if (typeof window !== 'undefined') {
        window.scriptsLoaded = true;
      }
      
      // Wait for jQuery and all plugins to be fully loaded before initializing
      if (typeof window !== 'undefined' && 
          window.jQuery && 
          window.jQueryLoaded && 
          window.jQuery.fn && 
          window.waypointsLoaded && 
          window.counterupLoaded &&
          window.wowLoaded) {
        // Remove any existing SlickNav elements
        const existingSlicknav = document.querySelector('.slicknav_menu');
        if (existingSlicknav) {
          existingSlicknav.remove();
        }
        
        // Initialize SlickNav only if it's available
        if (window.jQuery.fn.slicknav) {
          window.jQuery('#menu').slicknav({
            prependTo: '.responsive-menu',
            label: '',
            allowParentLinks: true,
            closedSymbol: '<i class="fa-solid fa-plus"></i>',
            openedSymbol: '<i class="fa-solid fa-minus"></i>'
          });
        }

        // Initialize Magic Cursor after GSAP and jQuery are loaded
        if ((window as any).gsap && window.jQuery) {
          // Magic cursor should be initialized automatically by the script
          // but we can ensure it's ready by checking if the Cursor class exists
          if ((window as any).Cursor) {
            console.log('Magic cursor initialized successfully');
          }
        }
        
        setIsInitialized(true);
      } else {
        // Retry after a short delay
        setTimeout(initializeScripts, 100);
      }
    };

    // Wait for hydration to complete and scripts to load
    const timer = setTimeout(() => {
      initializeScripts();
    }, 1000);

    return () => {
      mounted = false;
      clearTimeout(timer);
    };
  }, []);

  return isInitialized;
};
